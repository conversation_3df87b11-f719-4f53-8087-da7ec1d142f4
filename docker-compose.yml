version: "3"
volumes:
  files:
  vendor:
  lib:
  phpseclib:
  quickbooks:
  psql-data:
  psql-docsdata:
  psql-newInstall:
  data01:
    driver: local
  data02:
    driver: local
  data03:
    driver: local

services:
  bento:
    container_name: app
    build:
      context: ./_SERVICES/app/
    volumes:
      - ./_SERVICES/app/src:/var/www/html:cached
      - vendor:/var/www/html/api/vendor
      - lib:/var/www/html/api/lib
      - phpseclib:/var/www/html/api/phpseclib
      - quickbooks:/var/www/html/api/quickbooks
    ports:
      - 8080:80
      - 8082:82
    links:
      - db
      - docsdb
      - merge
    #       - es01
    environment:
      # test credentials
      - "TEST_ENVIRONMENT=ON"
      - 'BENTO_DATABASE_URL=db'
      - 'BENTO_DATABASE_WRITE_URL=db'
      - 'BENTO_DOCUMENTS_URL=docsdb'
      - 'BENTO_DOCUMENTS_WRITE_URL=docsdb'
      - 'BENTO_DATABASE_NAME=pagoda'
      - 'BENTO_DOCS_DATABASE_NAME=pagoda'
      - 'BENTO_DATABASE_USER=pagoda'
      - 'BENTO_DATABASE_PORT=5432'
      - 'BENTO_DATABASE_PASSWORD=gird6(vowed'
      - 'BENTO_DATABASE_SSL_FLAG=disable'
      - 'BENTO_WRITE_DATABASE_URL=db'
      - 'BENTO_WRITE_DOCUMENTS_URL=docsdb'
      - 'STRIPE_PK=pk_test_IhoVTZ8UmcTcKJjsA9uZnkLz'
      - 'STRIPE_SK=sk_test_GVtCpHdl2K1PMQC3d2IhISKZ'
      - 'ICG_SITEID=XABK'
      - 'ICG_SITEKEY=14128200'
      - 'ICG_APIKEY=e0b454998ebe'
      - 'ICG_SITEID_DREAM=XADO'
      - 'ICG_SITEKEY_DREAM=25515800'
      - 'ICG_APIKEY_DREAM=c3b2ca674f40'
      - 'ICG_GATEWAYLIVEMODE=false'
      - 'CSG_FORTE_API_ACCESS_ID=8adfb585f7a7cde0168cbebc52ccc1b2'
      - 'CSG_FORTE_SECURE_KEY=your_secure_key_here'
      - 'CSG_FORTE_LOCATION_ID=401809'
      - 'CSG_FORTE_ENVIRONMENT=sandbox'
    networks:
      app-env:
        aliases:
          - app
    #       elastic:
    depends_on:
      - db
      - docsdb
      - merge
  #       - es01
  #       - es02
  #       - es03

  # #1712: This is where the new merge service can
  # be defined.
  # merge: ...
  merge:
    container_name: merge
    build:
      context: ./_SERVICES/merge/
    volumes:
      - ./_SERVICES/merge/:/home/<USER>/app
      - ./_SERVICES/merge/node_modules:/home/<USER>/app/node_modules
    working_dir: /home/<USER>/app
    ports:
      - 8084:8084
    environment:
      - "TEST_ENVIRONMENT=ON"
      - "DATA_ENDPOINT=host.docker.internal"
      - "MERGE_ENDPOINT=host.docker.internal"
    networks:
      app-env:
        aliases:
          - merge
    #       elastic:

  # tests
  # test:
  #   container_name: test
  #   build:
  #     context: ./_SERVICES/test/
  #   ports:
  #     - 3010:3010
  #   links:
  #     - bento:bentoapi
  #   networks:
  #     - app-env

   # database service  
  db:
    container_name: db
    volumes:
      - psql-data:/var/lib/postgresql/data
    build:
      context: ./_SERVICES/database/
    restart: always
    ports:
      - 5432:5432
    environment:
      - 'POSTGRES_USER=pagoda'
      - 'POSTGRES_PASSWORD=gird6(vowed'
      - 'POSTGRES_DB=pagoda'
    networks:
      app-env:
      
  adminer:
    image: adminer
    restart: always
    ports:
      - 8090:8080
    networks:
      app-env:
      
  # documents service  
  docsdb:
    container_name: docsdb
    volumes:
      - psql-docsdata:/var/lib/postgresql/docsdata
    build:
      context: ./_SERVICES/docsdb/
    restart: always
    ports:
      - 5434:5434
    environment:
      - 'POSTGRES_USER=pagoda'
      - 'POSTGRES_PASSWORD=gird6(vowed'
      - 'POSTGRES_DB=pagoda'
    networks:
      app-env:

networks:
  app-env:
   driver: bridge