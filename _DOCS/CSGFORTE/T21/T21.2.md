# T21.2: Research ACH Processing APIs

**Task:** Research ACH processing APIs  
**Role:** Developer (D)  
**Status:** Complete  

## Prerequisites
- ✅ T20: CSG Forte Dex Platform Registration Complete
- ✅ T21.1: Credit Card API Research Complete
- ✅ API Credentials: 8adfb585f7a7cde0168cbebc52ccc1b2 / 4bbb4b9fbc8e58d47eddeab8030d190d

## ACH Processing Overview

### API Integration Method: CSG Forte Checkout v2 (ACH Mode)
**Selected for iFrame replacement** - matches our current iCheckGateway ACH integration pattern

### Key ACH API Details

#### ACH Payment Method Support
- **Method:** `echeck` in `allowed_methods` parameter
- **Default Display:** If `allowed_methods="echeck,visa,mast,disc"`, ACH form displays by default
- **Form Fields:** Account number, routing number, account type (checking/savings), account holder name

#### ACH Button Configuration
```javascript
// ACH-focused button (echeck first = default display)
allowed_methods="echeck,visa,mast,disc,amex"

// ACH-only button
allowed_methods="echeck"
```

### Integration Pattern for Bento

#### Current iCheckGateway ACH Integration (TO REPLACE)
```javascript
// In contact-payment-sources.js
ui.buttons.makeNode("createiCGACH", "button", {
  css: "pda-btnOutline-blue",
  text: '<i class="fa fa-university"></i> Bank Account (ACH)',
})
.notify("click", {
  type: "paymentMethodRun",
  data: {
    run: addiCGACHtoCustomer.bind(ui, contactId, options),
  },
});
```

#### New CSG Forte ACH Pattern (TO IMPLEMENT)
```javascript
// In contact-payment-sources.js
ui.buttons.makeNode("createCSGForteACH", "button", {
  css: "pda-btnOutline-blue",
  text: '<i class="fa fa-university"></i> CSG Forte Bank Account',
})
.notify("click", {
  type: "paymentMethodRun",
  data: {
    run: addACHtoCSGForteCustomer.bind(ui, contactId, options),
  },
});
```

## ACH Checkout v2 Implementation Details

### Button Parameters for ACH
```html
<button 
  api_access_id="8adfb585f7a7cde0168cbebc52ccc1b2"
  location_id="loc_401809" 
  hash_method="sha256"
  version_number="2.0"
  method="sale"
  total_amount="10.00"
  allowed_methods="echeck"
  sec_code="WEB"
  order_number="INV-12345"
  signature="[HMAC_SHA256_SIGNATURE]"
  utc_time="[UTC_TICKS]"
  callback="onACHPaymentCallback">
  Pay with Bank Account
</button>
```

### ACH-Specific Parameters

#### SEC Codes for ACH
- **Default:** `WEB` (used if not specified)
- **Common Options:** `WEB`, `TEL`, `PPD`, `CCD`
- **Parameter:** `sec_code="WEB"`

#### Account Types Supported
- Checking accounts
- Savings accounts

### ACH Authentication Signature
```javascript
// ACH signature follows same pattern as CC
const signatureString = `${api_access_id}|${method}|${version_number}|${total_amount}|${utc_time}|${order_number}|${customer_token}|${paymethod_token}`;
const signature = HMACSHA256(signatureString, api_secure_key);
```

### Response Handling Pattern for ACH
```javascript
// Similar to existing iCG ACH pattern
window.addEventListener('message', csgForteACHResponseHandler);

function csgForteACHResponseHandler(e) {
  if (e.data.action == "dismissFrame") {
    return;
  } else {
    var csgForteResponse = e.data;
    if (typeof csgForteResponse != "undefined" && 
        csgForteResponse.response_code == "A01" && // ACH Approval
        csgForteResponse.method_used == "echeck" &&
        csgForteResponse.trace_number != undefined) {
      
      // Process successful ACH payment
      processCSGForteACHPayment(csgForteResponse);
    }
  }
}
```

## ACH Payment Processing Flow

### Step 1: Customer Initiates ACH Payment
- Customer clicks "CSG Forte Bank Account" button
- iFrame loads with CSG Forte Checkout v2 ACH form

### Step 2: ACH Collection
- Customer enters bank account details in secure iFrame
- Account holder name, routing number, account number, account type
- CSG Forte handles ACH compliance and validation

### Step 3: ACH Response Processing  
- Success response contains `trace_number` and `method_used="echeck"`
- Create payment object in Bento with CSG Forte ACH metadata
- Update invoice balances
- Send email notifications

### Step 4: ACH Payment Object Creation
```php
// In new CSGForteService.php (similar to iCheckGatewayService.php)
$paymentObject = $this->sb->pgObjects->create('payments', array(
    'main_object' => $proposal['id'],
    'amount' => $amount,
    'fee' => $transactionFee,
    'csg_forte_transaction_id' => $csgForteResponse->trace_number,
    'csg_forte_method' => 'echeck',
    'invoice' => $inv["id"],
    'test_payment' => $this->testKey,
    'manual_payment' => false,
    'owner' => $paymentOwnerId,
));
```

## ACH-Specific Response Parameters

### Success Response Fields
```javascript
{
  "event": "success",
  "method": "sale",
  "trace_number": "fb25a0c5-c5a5-4505-9c56-4297799aeb77",
  "response_code": "A01",
  "response_description": "APPROVAL",
  "total_amount": "10.00",
  "method_used": "echeck",
  "last_4": "6789", // Last 4 of account number
  "version_number": "2.0",
  "hash_method": "sha256",
  "signature": "c1d704c4711595f48e1552a9af9b8ada",
  "utc_time": "635210889954381263"
}
```

### ACH Hold Period Information
- **Processing Time:** ACH transactions typically take 1-4 business days to clear
- **Status Updates:** Available via webhook or REST API polling
- **Initial Status:** Transaction approved but funds pending

## Metadata Field Reuse Strategy

### Existing iCG ACH Metadata Fields (TO REUSE)
- Payment object structure
- Invoice reconciliation logic  
- Email notification system
- ACH processing patterns

### New CSG Forte ACH Fields (TO ADD)
- `csg_forte_transaction_id` (replaces `icg_transaction_id`)
- `csg_forte_method` = "echeck"
- `csg_forte_response` (full response object)
- ACH-specific status tracking

## Testing Requirements

### Sandbox Test ACH Accounts
```
Valid Routing Numbers:
- ********* (TD Bank)
- ********* (Chase Bank)
- ********* (Bank of America)
- ********* (Bank of America)

Valid Account Numbers:
- Any 4-17 digit number
- Test: *********

Account Types:
- checking
- savings
```

### ACH Test Scenarios
- [ ] Successful ACH payment processing
- [ ] Invalid routing number handling
- [ ] Invalid account number handling  
- [ ] Insufficient funds handling
- [ ] Account closed handling
- [ ] ACH return processing

## Success Criteria
- [x] CSG Forte ACH API parameters documented
- [x] ACH integration pattern defined for Bento
- [x] ACH response handling structure planned
- [x] ACH payment object mapping completed
- [x] ACH test scenarios identified
- [x] SEC code handling documented

## Next Steps
After ACH API research completion:
- T21.3: Research iFrame integration options
- T21.4: Research error handling patterns  
- T27.x: Begin ACH button implementation

## Key Differences from Credit Cards
1. **Method Used:** `echeck` vs `visa`/`mast`/`disc`/`amex`
2. **Processing Time:** 1-4 business days vs immediate
3. **SEC Codes:** Required for ACH compliance
4. **Account Types:** Checking/savings vs card types
5. **Response Fields:** `last_4` refers to account number, not card

## Notes
- ACH transactions use same Checkout v2 integration as credit cards
- `allowed_methods` parameter controls payment type availability
- SEC code defaults to "WEB" if not specified
- Response patterns identical to CC with different field values
- Webhook support available for ACH status updates
