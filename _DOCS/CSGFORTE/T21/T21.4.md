# T21.4: Research Error Handling Patterns

**Task:** Research error handling patterns  
**Role:** Developer (D)  
**Status:** Complete  

## Prerequisites
- ✅ T20: CSG Forte Dex Platform Registration Complete
- ✅ T21.1-T21.3: API and iFrame Research Complete
- ✅ API Credentials: 8adfb585f7a7cde0168cbebc52ccc1b2 / 4bbb4b9fbc8e58d47eddeab8030d190d

## Error Handling Overview

### CSG Forte Error Categories
1. **Authentication Errors** - Invalid signatures, expired tokens
2. **Validation Errors** - Invalid parameters, malformed data  
3. **Transaction Errors** - Declined cards, insufficient funds
4. **System Errors** - Network issues, service unavailability
5. **User Errors** - Browser compatibility, modal dismissal

## Response Event Types

### Callback Events for Error Handling
```javascript
window.addEventListener('message', function(e) {
  var response = e.data;
  
  switch(response.event) {
    case 'success':
      handleSuccess(response);
      break;
    case 'failure':
      handleTransactionFailure(response);  // Declined transactions
      break;
    case 'error':
      handleSystemError(response);         // System/validation errors
      break;
    case 'abort':
      handleUserAbort(response);           // User canceled
      break;
    case 'expired':
      handleSessionExpired(response);      // Session timeout
      break;
  }
});
```

## Transaction Response Codes

### Credit Card Response Codes
```javascript
// Common CC response codes to handle
const CC_RESPONSE_CODES = {
  'A01': 'APPROVAL',           // Success
  'D01': 'REFER TO ISSUER',    // Contact bank
  'D02': 'REFER TO ISSUER',    // Contact bank  
  'D03': 'INVALID MERCHANT',   // Configuration issue
  'D05': 'DO NOT HONOR',       // Generic decline
  'D12': 'INVALID TRANSACTION', // Invalid request
  'D13': 'INVALID AMOUNT',     // Amount issue
  'D14': 'INVALID CARD',       // Bad card number
  'D15': 'NO SUCH ISSUER',     // Invalid card
  'D19': 'RE-ENTER TRANSACTION', // Try again
  'D41': 'LOST CARD',          // Lost/stolen
  'D43': 'STOLEN CARD',        // Stolen card
  'D51': 'INSUFFICIENT FUNDS', // No money
  'D54': 'EXPIRED CARD',       // Card expired
  'D55': 'INCORRECT PIN',      // PIN issue
  'D57': 'TRANSACTION NOT PERMITTED', // Restricted
  'D61': 'EXCEEDS WITHDRAWAL LIMIT',  // Limit exceeded
  'D62': 'RESTRICTED CARD',    // Card restricted
  'D65': 'ACTIVITY LIMIT EXCEEDED',   // Too many attempts
  'D78': 'NO ACCOUNT',         // Account not found
  'D96': 'SYSTEM MALFUNCTION', // System error
};
```

### ACH Response Codes
```javascript
// Common ACH response codes to handle
const ACH_RESPONSE_CODES = {
  'A01': 'APPROVAL',           // Success
  'D01': 'REFER TO CUSTOMER',  // Contact customer
  'D02': 'REFER TO CUSTOMER',  // Contact customer
  'D05': 'DO NOT HONOR',       // Generic decline  
  'D12': 'INVALID TRANSACTION', // Invalid request
  'D13': 'INVALID AMOUNT',     // Amount issue
  'D19': 'RE-ENTER TRANSACTION', // Try again
  'R01': 'INSUFFICIENT FUNDS', // NSF
  'R02': 'ACCOUNT CLOSED',     // Closed account
  'R03': 'NO ACCOUNT',         // Account not found
  'R04': 'INVALID ACCOUNT',    // Bad account number
  'R05': 'UNAUTHORIZED DEBIT', // Not authorized
  'R06': 'RETURNED PER INSTRUCTION', // Customer request
  'R07': 'AUTHORIZATION REVOKED',     // Auth revoked
  'R08': 'PAYMENT STOPPED',    // Stop payment
  'R09': 'UNCOLLECTED FUNDS', // Funds not available
  'R10': 'CUSTOMER ADVISES NOT AUTHORIZED', // Unauthorized
};
```

## Error Response Structures

### Failure Event Response
```javascript
// Transaction declined/failed
{
  "event": "failure",
  "method": "sale",
  "request_id": "21e2d11d-bfd9-4991-ca50-16170bc21329",
  "response_code": "D51",
  "response_description": "INSUFFICIENT FUNDS",
  "version_number": "2.0",
  "trace_number": "********-6eb2-483f-81af-1d787a903f5c",
  "total_amount": "51.23",
  "last_4": "1111",
  "method_used": "visa",
  "signature": "e83dee1c9fa2067786fb53c149ebbe62",
  "utc_time": "635295420748992999",
  "hash_method": "sha256"
}
```

### Error Event Response
```javascript
// System/validation error
{
  "event": "error",
  "msg": "Invalid total_amount: 1-9.5;5d"
}

// Authentication error
{
  "event": "error", 
  "msg": "Invalid authentication."
}

// Parameter error
{
  "event": "error",
  "msg": "The parameter api_access_id is required."
}
```

## Error Handling Implementation

### Comprehensive Error Handler
```javascript
function handleCSGForteErrors(response) {
  switch(response.event) {
    case 'failure':
      handleTransactionFailure(response);
      break;
    case 'error':
      handleSystemError(response);
      break;
    case 'abort':
      handleUserAbort(response);
      break;
    case 'expired':
      handleSessionExpired(response);
      break;
    default:
      handleUnknownError(response);
  }
}

function handleTransactionFailure(response) {
  var responseCode = response.response_code;
  var message = getCustomerFriendlyMessage(responseCode);
  
  // Log for debugging
  console.error('CSG Forte Transaction Failed:', {
    code: responseCode,
    description: response.response_description,
    trace_number: response.trace_number,
    method: response.method_used
  });
  
  // Show user-friendly message
  showErrorModal(message);
  
  // Specific handling for common errors
  switch(responseCode) {
    case 'D51': // Insufficient funds
      suggestAlternativePayment();
      break;
    case 'D54': // Expired card
      suggestCardUpdate();
      break;
    case 'D14': // Invalid card
      suggestCardReentry();
      break;
  }
}

function handleSystemError(response) {
  console.error('CSG Forte System Error:', response.msg);
  
  // Parse common system errors
  if (response.msg.includes('Invalid authentication')) {
    // Log authentication failure - likely signature issue
    logAuthenticationError();
    showSystemErrorMessage();
  } else if (response.msg.includes('Invalid total_amount')) {
    // Amount formatting issue
    showAmountErrorMessage();
  } else if (response.msg.includes('browser you are using is not supported')) {
    // Browser compatibility issue
    showBrowserErrorMessage();
  } else {
    // Generic system error
    showGenericSystemError();
  }
}
```

### User-Friendly Error Messages
```javascript
function getCustomerFriendlyMessage(responseCode) {
  const CUSTOMER_MESSAGES = {
    'D51': 'Insufficient funds. Please try a different payment method or add funds to your account.',
    'D54': 'Your card has expired. Please update your card information and try again.',
    'D14': 'Invalid card number. Please check your card details and try again.',
    'D41': 'This card has been reported lost. Please use a different payment method.',
    'D43': 'This card has been reported stolen. Please use a different payment method.',
    'D05': 'Transaction declined. Please contact your bank or try a different payment method.',
    'D65': 'Too many attempts. Please wait a few minutes before trying again.',
    'R01': 'Insufficient funds in bank account. Please try a different account or payment method.',
    'R02': 'Bank account is closed. Please use a different account.',
    'R03': 'Bank account not found. Please verify account details.',
    'R04': 'Invalid account number. Please check your bank account details.',
    'DEFAULT': 'Transaction could not be processed. Please try again or contact support.'
  };
  
  return CUSTOMER_MESSAGES[responseCode] || CUSTOMER_MESSAGES['DEFAULT'];
}
```

## Common System Error Messages

### Authentication Errors
```javascript
const AUTH_ERROR_MESSAGES = [
  'Invalid authentication.',
  'The parameter api_access_id is required.',
  'Invalid parameter api_access_id.',
  'Invalid parameter hash_method: "______." Use sha256.',
  'Invalid parameter method: "______." Use sale/schedule/token.',
  'The parameter utc_time is required.'
];
```

### Validation Errors  
```javascript
const VALIDATION_ERROR_MESSAGES = [
  'Invalid total_amount for scheduled transaction: "______."',
  'Invalid parameter total_amount for scheduled transaction: "______."',
  'The parameter version_number is required.',
  'Email address is invalid.',
  'State is not valid.',
  'Routing number is invalid.',
  'Invalid parameter schedule_quantity: "______."',
  'Invalid parameter schedule_continuous: "______", Use true/false.',
  'Invalid parameter schedule_frequency: "______". Use weekly, bi-weekly, monthly, bi-monthly, quarterly, semi-annually, annually. Use 0 for single future transaction.'
];
```

### Browser Compatibility Errors
```javascript
const BROWSER_ERROR_MESSAGES = [
  'The browser you are using is not supported. Please download the latest version of your browser to use Checkout.',
  'The browser you are using is not supported. Checkout supports the latest versions of the following browsers: Internet Explorer, Firefox, Chrome, and Safari.',
  'Please either disable compatibility view or upgrade your browser to proceed.'
];
```

## Error Logging and Monitoring

### Comprehensive Error Logging
```javascript
function logCSGForteError(response, context) {
  var errorData = {
    timestamp: new Date().toISOString(),
    event_type: response.event,
    error_code: response.response_code || 'SYSTEM_ERROR',
    error_message: response.response_description || response.msg,
    trace_number: response.trace_number,
    request_id: response.request_id,
    method_used: response.method_used,
    amount: response.total_amount,
    last_4: response.last_4,
    context: context,
    user_agent: navigator.userAgent,
    page_url: window.location.href
  };
  
  // Send to monitoring system
  sendErrorToMonitoring(errorData);
  
  // Log to console for debugging
  console.error('CSG Forte Error:', errorData);
}
```

### Error Recovery Strategies
```javascript
function handleErrorRecovery(response) {
  switch(response.response_code) {
    case 'D19': // Re-enter transaction
      // Allow immediate retry
      enableRetryButton();
      break;
      
    case 'D65': // Activity limit exceeded
      // Implement temporary cooldown
      disablePaymentFor(300000); // 5 minutes
      showCooldownMessage();
      break;
      
    case 'D96': // System malfunction
      // Switch to backup payment method
      offerAlternativePaymentMethods();
      break;
      
    case 'D14': // Invalid card
      // Clear form and allow re-entry
      clearPaymentForm();
      focusCardNumberField();
      break;
  }
}
```

## Integration with Existing Error Handling

### Reuse Bento Error Patterns
```javascript
// Integrate with existing notification system
function showCSGForteError(message) {
  // Use existing Bento notification system
  ui.notify({
    type: 'error',
    message: message,
    timeout: 5000
  });
}

// Integrate with existing logging
function logToMailSpon(errorData) {
  // Use existing MailSpon logging system
  sendMailSponNotification({
    type: 'csg_forte_error',
    data: errorData,
    priority: 'high'
  });
}
```

### Error Handling Flow Integration
```javascript
// Replace existing Stripe/iCG error handling
function processPaymentError(response) {
  // Log the error
  logCSGForteError(response, 'payment_processing');
  
  // Handle based on error type
  handleCSGForteErrors(response);
  
  // Update UI state
  resetPaymentUI();
  
  // Notify monitoring systems
  notifyErrorMonitoring(response);
}
```

## AVS and CVV Error Handling

### Address Verification Service (AVS) Codes
```javascript
const AVS_CODES = {
  'A': 'Address matches, ZIP does not',
  'B': 'Address information not provided',
  'E': 'AVS error',
  'G': 'Non-U.S. card issuing bank',
  'N': 'No match on address or ZIP',
  'P': 'AVS not applicable for this transaction',
  'R': 'Retry - system unavailable',
  'S': 'Service not supported by issuer',
  'U': 'Address information is unavailable',
  'W': 'Nine-digit ZIP matches, address does not',
  'X': 'Address and nine-digit ZIP match',
  'Y': 'Address and five-digit ZIP match',
  'Z': 'Five-digit ZIP matches, address does not'
};
```

### CVV Response Codes
```javascript
const CVV_CODES = {
  'M': 'CVV2 match',
  'N': 'CVV2 no match',
  'P': 'Not processed',
  'S': 'Service not supported by issuer',
  'U': 'Service unavailable'
};
```

## Success Criteria
- [x] Response event types documented
- [x] Transaction response codes catalogued
- [x] Error response structures defined
- [x] Comprehensive error handling implemented
- [x] User-friendly error messages created
- [x] Error logging and monitoring planned
- [x] Error recovery strategies defined
- [x] Integration with existing Bento patterns
- [x] AVS and CVV error handling documented

## Next Steps
After error handling research completion:
- T21.5: Research fee structures
- T23.x: Begin button implementation with error handling
- T24.x: Implement error handling in iFrame integration

## Key Error Handling Principles
1. **User-Friendly Messages:** Convert technical codes to understandable language
2. **Comprehensive Logging:** Track all errors for monitoring and debugging
3. **Graceful Degradation:** Offer alternatives when primary method fails
4. **Recovery Strategies:** Allow retries and provide clear next steps
5. **Security:** Never expose sensitive authentication details in errors
6. **Consistency:** Maintain error handling patterns with existing Bento code

## Notes
- CSG Forte provides detailed response codes for both success and failure
- Error events vs failure events serve different purposes
- Authentication errors indicate integration issues, not user errors
- Browser compatibility should be checked before payment initiation
- Error recovery strategies can improve conversion rates
- Integration with existing Bento error systems maintains consistency
