# T21.5: Research Fee Structure

**Task:** Research fee structures  
**Role:** Developer (D)  
**Status:** Complete  

## Prerequisites
- ✅ T20: CSG Forte Dex Platform Registration Complete
- ✅ T21.1-T21.4: API, iFrame, and Error Handling Research Complete
- ✅ API Credentials: 8adfb585f7a7cde0168cbebc52ccc1b2 / 4bbb4b9fbc8e58d47eddeab8030d190d

## Fee Structure Overview

### CSG Forte Fee Categories
1. **Service Fees** - Convenience fees added to transactions
2. **Processing Fees** - Standard payment processing costs
3. **Transaction Fees** - Per-transaction charges
4. **Monthly Fees** - Account maintenance costs
5. **Setup Fees** - Initial configuration costs

## Service Fee Implementation

### Automatic Service Fee Calculation
CSG Forte Checkout v2 supports automatic service fee calculation and addition to transaction amounts.

```javascript
// Service fee response parameters
{
  "subtotal_amount": "50.00",      // Original amount
  "service_fee_amount": "1.23",    // Calculated fee (2.45%)
  "total_amount": "51.23",         // Final charged amount
  "authorization_amount": "51.23"   // Amount authorized
}
```

### Service Fee Configuration
Service fees are configured in the CSG Forte Dex portal and automatically calculated during transactions:

- **Percentage-based:** e.g., 2.45% of transaction amount
- **Flat fee:** e.g., $1.50 per transaction  
- **Tiered structure:** Different rates based on amount ranges
- **Payment method specific:** Different rates for CC vs ACH

### Service Fee Response Handling
```javascript
function processServiceFeeResponse(response) {
  var feeData = {
    subtotal: parseFloat(response.subtotal_amount),
    service_fee: parseFloat(response.service_fee_amount), 
    total: parseFloat(response.total_amount)
  };
  
  // Log fee calculation for reconciliation
  console.log('Service Fee Breakdown:', feeData);
  
  // Store fee data for reporting
  storeFeeData(feeData);
  
  // Update UI to show fee breakdown
  displayFeeBreakdown(feeData);
}
```

## Processing Fee Structure

### Credit Card Processing Fees
Typical CSG Forte credit card processing fees (merchant-specific):

```javascript
// Example fee structure (varies by merchant agreement)
const CC_PROCESSING_FEES = {
  'visa': {
    'rate': '2.9%',
    'transaction_fee': '$0.30',
    'description': 'Visa/Mastercard standard rate'
  },
  'mast': {
    'rate': '2.9%', 
    'transaction_fee': '$0.30',
    'description': 'Visa/Mastercard standard rate'
  },
  'amex': {
    'rate': '3.5%',
    'transaction_fee': '$0.30', 
    'description': 'American Express premium rate'
  },
  'disc': {
    'rate': '2.9%',
    'transaction_fee': '$0.30',
    'description': 'Discover standard rate'
  }
};
```

### ACH Processing Fees
Typical CSG Forte ACH processing fees (merchant-specific):

```javascript
// Example ACH fee structure (varies by merchant agreement)
const ACH_PROCESSING_FEES = {
  'standard': {
    'rate': '1.0%',
    'transaction_fee': '$0.50',
    'max_fee': '$5.00',
    'description': 'Standard ACH processing'
  },
  'same_day': {
    'rate': '1.0%',
    'transaction_fee': '$1.50',
    'max_fee': '$15.00', 
    'description': 'Same-day ACH processing'
  }
};
```

## Fee Calculation and Display

### Client-Side Fee Estimation
```javascript
function estimateProcessingFees(amount, paymentMethod) {
  var fees = {
    processing_rate: 0,
    transaction_fee: 0,
    estimated_total: 0
  };
  
  switch(paymentMethod) {
    case 'credit_card':
      fees.processing_rate = amount * 0.029; // 2.9%
      fees.transaction_fee = 0.30;
      break;
    case 'ach':
      fees.processing_rate = amount * 0.01; // 1.0%
      fees.transaction_fee = 0.50;
      fees.processing_rate = Math.min(fees.processing_rate, 5.00); // Max $5
      break;
  }
  
  fees.estimated_total = fees.processing_rate + fees.transaction_fee;
  return fees;
}
```

### Fee Display in UI
```javascript
function displayFeeBreakdown(subtotal, paymentMethod) {
  var fees = estimateProcessingFees(subtotal, paymentMethod);
  
  var feeHTML = `
    <div class="fee-breakdown">
      <div class="fee-line">
        <span>Subtotal:</span>
        <span>$${subtotal.toFixed(2)}</span>
      </div>
      <div class="fee-line">
        <span>Processing Fee:</span>
        <span>$${fees.estimated_total.toFixed(2)}</span>
      </div>
      <div class="fee-line total">
        <span>Total:</span>
        <span>$${(subtotal + fees.estimated_total).toFixed(2)}</span>
      </div>
    </div>
  `;
  
  document.getElementById('fee-display').innerHTML = feeHTML;
}
```

## Fee Integration with Bento

### Payment Object Fee Storage
```php
// In CSGForteService.php - store fee data
$paymentObject = $this->sb->pgObjects->create('payments', array(
    'main_object' => $proposal['id'],
    'amount' => $response->subtotal_amount,           // Original amount
    'fee' => $response->service_fee_amount,           // Service fee
    'processing_fee' => $calculatedProcessingFee,     // Our processing cost
    'total_amount' => $response->total_amount,        // Total charged
    'csg_forte_transaction_id' => $response->trace_number,
    'invoice' => $inv["id"],
    'test_payment' => $this->testKey,
    'manual_payment' => false,
    'owner' => $paymentOwnerId,
));
```

### Fee Reconciliation
```php
// Track fee breakdown for accounting
$feeBreakdown = array(
    'subtotal' => $response->subtotal_amount,
    'service_fee' => $response->service_fee_amount,
    'processing_fee' => $calculatedProcessingFee,
    'total_charged' => $response->total_amount,
    'net_received' => $response->subtotal_amount - $calculatedProcessingFee
);

// Store for reporting
$this->storeFeeBreakdown($feeBreakdown);
```

## Fee Compliance and Transparency

### Service Fee Disclosure
```javascript
// Display service fee disclosure before payment
function showServiceFeeDisclosure(amount, feeAmount) {
  var disclosureHTML = `
    <div class="service-fee-disclosure">
      <h3>Convenience Fee Notice</h3>
      <p>A convenience fee of $${feeAmount.toFixed(2)} will be added to your payment of $${amount.toFixed(2)}.</p>
      <p>Total amount to be charged: $${(amount + feeAmount).toFixed(2)}</p>
      <p>This fee is charged by our payment processor for the convenience of paying online.</p>
      <label>
        <input type="checkbox" id="fee-acknowledgment" required>
        I acknowledge and agree to pay the convenience fee
      </label>
    </div>
  `;
  
  return disclosureHTML;
}
```

### Fee Opt-out Options  
```javascript
// Provide alternative payment methods without fees
function showFeeAlternatives() {
  var alternativeHTML = `
    <div class="fee-alternatives">
      <h4>Avoid Convenience Fees</h4>
      <p>To avoid the convenience fee, you may:</p>
      <ul>
        <li>Mail a check to our billing address</li>
        <li>Call our office to pay by phone</li>
        <li>Visit our office in person</li>
      </ul>
      <button onclick="showMailingAddress()">Get Mailing Address</button>
      <button onclick="showPhonePayment()">Phone Payment Info</button>
    </div>
  `;
  
  return alternativeHTML;
}
```

## Fee Reporting and Analytics

### Fee Tracking Dashboard
```javascript
// Track fee metrics for reporting
function trackFeeMetrics(response) {
  var metrics = {
    transaction_id: response.trace_number,
    subtotal: parseFloat(response.subtotal_amount),
    service_fee: parseFloat(response.service_fee_amount),
    total: parseFloat(response.total_amount),
    payment_method: response.method_used,
    timestamp: new Date().toISOString()
  };
  
  // Send to analytics
  sendFeeAnalytics(metrics);
  
  // Update dashboard
  updateFeeDashboard(metrics);
}
```

### Monthly Fee Reconciliation
```php
// Generate monthly fee reports
class CSGForteFeeReporting {
    public function generateMonthlyReport($month, $year) {
        $report = array(
            'total_transactions' => 0,
            'total_volume' => 0,
            'total_service_fees' => 0,
            'total_processing_fees' => 0,
            'net_revenue' => 0,
            'fee_breakdown' => array(
                'credit_card' => array(),
                'ach' => array()
            )
        );
        
        // Calculate metrics
        $transactions = $this->getTransactionsForPeriod($month, $year);
        foreach($transactions as $txn) {
            $report['total_transactions']++;
            $report['total_volume'] += $txn['total_amount'];
            $report['total_service_fees'] += $txn['service_fee'];
            $report['total_processing_fees'] += $txn['processing_fee'];
        }
        
        $report['net_revenue'] = $report['total_volume'] - $report['total_processing_fees'];
        
        return $report;
    }
}
```

## Fee Configuration Management

### Environment-Specific Fee Settings
```javascript
// Fee configuration by environment
const FEE_CONFIG = {
  'sandbox': {
    'service_fee_enabled': true,
    'service_fee_rate': 0.0245, // 2.45%
    'min_service_fee': 0.50,
    'max_service_fee': 25.00
  },
  'production': {
    'service_fee_enabled': true,
    'service_fee_rate': 0.0295, // 2.95%
    'min_service_fee': 1.00,
    'max_service_fee': 50.00
  }
};
```

### Dynamic Fee Updates
```javascript
// Allow fee configuration updates without code changes
function updateFeeConfiguration(newConfig) {
  // Validate fee configuration
  if (validateFeeConfig(newConfig)) {
    // Update configuration
    localStorage.setItem('csg_forte_fee_config', JSON.stringify(newConfig));
    
    // Refresh fee displays
    refreshFeeDisplays();
    
    // Log configuration change
    logConfigurationChange('fee_config', newConfig);
  }
}
```

## Integration with Existing Fee Systems

### Reuse Bento Fee Patterns
```php
// Integrate with existing fee calculation systems
class CSGForteService extends BasePaymentService {
    
    protected function calculateTotalFees($amount, $paymentMethod) {
        // Use existing Bento fee calculation patterns
        $baseFees = parent::calculateProcessingFees($amount, $paymentMethod);
        
        // Add CSG Forte specific fees
        $csgForteFees = $this->calculateCSGForteFees($amount, $paymentMethod);
        
        return array_merge($baseFees, $csgForteFees);
    }
    
    protected function calculateCSGForteFees($amount, $paymentMethod) {
        $fees = array();
        
        switch($paymentMethod) {
            case 'credit_card':
                $fees['processing_rate'] = $amount * 0.029;
                $fees['transaction_fee'] = 0.30;
                break;
            case 'ach':
                $fees['processing_rate'] = min($amount * 0.01, 5.00);
                $fees['transaction_fee'] = 0.50;
                break;
        }
        
        return $fees;
    }
}
```

## Success Criteria
- [x] Service fee implementation documented
- [x] Processing fee structure researched
- [x] Fee calculation methods defined
- [x] Fee display and transparency planned
- [x] Fee compliance requirements identified
- [x] Fee reporting and analytics designed
- [x] Fee configuration management planned
- [x] Integration with existing Bento patterns

## Next Steps
After fee structure research completion:
- T22.x: Local development environment setup
- T23.x: Begin button implementation with fee handling
- Implementation teams can proceed with T22+ tasks

## Key Fee Management Principles
1. **Transparency:** Clearly display all fees before payment
2. **Compliance:** Meet legal requirements for fee disclosure
3. **Flexibility:** Support multiple fee structures and configurations
4. **Accuracy:** Ensure fee calculations match payment processor
5. **Reporting:** Track fees for reconciliation and analytics
6. **Integration:** Maintain consistency with existing Bento fee patterns

## Notes
- Service fees are automatically calculated by CSG Forte Checkout v2
- Processing fees are separate from service fees  
- Fee disclosure is required for compliance
- Fee structures vary by merchant agreement
- Real-time fee calculation improves user experience
- Fee reporting enables financial reconciliation
- Configuration management allows fee updates without code changes

---

## T21 Research Phase Complete ✅

**All T21 subtasks completed:**
- ✅ T21.1: Credit Card API Research
- ✅ T21.2: ACH API Research  
- ✅ T21.3: iFrame Integration Research
- ✅ T21.4: Error Handling Research
- ✅ T21.5: Fee Structure Research

**Ready for next phase:** T22 Local Development Environment Setup
