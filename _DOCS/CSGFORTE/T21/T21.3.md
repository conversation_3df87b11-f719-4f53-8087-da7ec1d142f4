# T21.3: Research iFrame Integration Options

**Task:** Research iFrame integration options  
**Role:** Developer (D)  
**Status:** Complete  

## Prerequisites
- ✅ T20: CSG Forte Dex Platform Registration Complete
- ✅ T21.1: Credit Card API Research Complete
- ✅ T21.2: ACH API Research Complete
- ✅ API Credentials: ******************************** / 4bbb4b9fbc8e58d47eddeab8030d190d

## iFrame Integration Overview

### CSG Forte Checkout v2 Modal System
**Selected Method:** Modal overlay system (not traditional iFrame)
- Uses JavaScript to create secure payment modal
- Maintains PCI compliance by keeping payment data off merchant server
- Similar user experience to existing Stripe/iCG iFrame integrations

### Integration Architecture

#### Script Loading
```html
<!-- Sandbox Environment -->
<script type="text/javascript" src="https://sandbox.forte.net/checkout/v2/js"></script>

<!-- Production Environment -->
<script type="text/javascript" src="https://checkout.forte.net/v2/js"></script>
```

#### Button-Triggered Modal <PERSON>
```html
<!-- CSG Forte payment button triggers modal -->
<button 
  api_access_id="********************************"
  location_id="loc_401809"
  hash_method="sha256"
  version_number="2.0"
  method="sale"
  total_amount="10.00"
  order_number="INV-12345"
  allowed_methods="visa,mast,disc,amex,echeck"
  signature="[SIGNATURE]"
  utc_time="[UTC_TICKS]"
  callback="onPaymentComplete">
  Pay Now
</button>
```

## Integration Patterns for Bento

### Current Integration Patterns (TO REPLACE)

#### Stripe Credit Card iFrame (contact-payment-sources.js)
```javascript
// Current Stripe pattern
addCCtoStripeCustomer: function(contactId, stripeId, options) {
  // Stripe Elements iFrame integration
  var elements = stripe.elements();
  var cardElement = elements.create('card');
  cardElement.mount('#card-element');
}
```

#### iCheckGateway ACH iFrame (contact-payment-sources.js)  
```javascript
// Current iCG pattern
addiCGACHtoCustomer: function(contactId, options) {
  // iCG iFrame integration
  var iFrame = document.createElement('iframe');
  iFrame.src = 'https://secure.icheckgateway.com/payment-form';
}
```

### New CSG Forte Integration Pattern (TO IMPLEMENT)

#### Credit Card Modal Integration
```javascript
// New CSG Forte CC pattern
addCCtoCSGForteCustomer: function(contactId, options) {
  // Create CSG Forte button dynamically
  var button = document.createElement('button');
  button.setAttribute('api_access_id', '********************************');
  button.setAttribute('location_id', 'loc_401809');
  button.setAttribute('hash_method', 'sha256');
  button.setAttribute('version_number', '2.0');
  button.setAttribute('method', 'sale');
  button.setAttribute('total_amount', options.amount);
  button.setAttribute('allowed_methods', 'visa,mast,disc,amex');
  button.setAttribute('order_number', options.invoiceNumber);
  button.setAttribute('signature', generateSignature());
  button.setAttribute('utc_time', getUTCTime());
  button.setAttribute('callback', 'onCSGForteCCComplete');
  
  // Trigger modal
  button.click();
}
```

#### ACH Modal Integration
```javascript
// New CSG Forte ACH pattern  
addACHtoCSGForteCustomer: function(contactId, options) {
  // Create CSG Forte ACH button dynamically
  var button = document.createElement('button');
  button.setAttribute('api_access_id', '********************************');
  button.setAttribute('location_id', 'loc_401809');
  button.setAttribute('hash_method', 'sha256');
  button.setAttribute('version_number', '2.0');
  button.setAttribute('method', 'sale');
  button.setAttribute('total_amount', options.amount);
  button.setAttribute('allowed_methods', 'echeck');
  button.setAttribute('sec_code', 'WEB');
  button.setAttribute('order_number', options.invoiceNumber);
  button.setAttribute('signature', generateSignature());
  button.setAttribute('utc_time', getUTCTime());
  button.setAttribute('callback', 'onCSGForteACHComplete');
  
  // Trigger modal
  button.click();
}
```

## Modal Customization Options

### Visual Customization (Limited)
- Button text: `button_text` parameter
- Payment methods: `allowed_methods` parameter  
- Field visibility: `*_attr="hide"` parameters
- Field requirements: `*_attr="required"` parameters

### Field Control Attributes
```html
<!-- Hide billing fields -->
billing_name_attr="hide"
billing_street_line1_attr="hide"

<!-- Make fields required -->
billing_postal_code_attr="required"
tax_amount_attr="required"

<!-- Make fields editable -->
total_amount_attr="edit"
tax_amount_attr="edit"
```

### Pre-population Support
```html
<!-- Pre-populate customer data -->
billing_name="John Doe"
billing_email_address="<EMAIL>"
billing_street_line1="123 Main St"
billing_locality="Dallas"
billing_region="TX"
billing_postal_code="75201"
billing_country="US"
```

## Response Handling Architecture

### Message Event Listener Pattern
```javascript
// Replace existing Stripe/iCG listeners
window.addEventListener('message', function(e) {
  if (e.origin !== 'https://sandbox.forte.net') return; // Security check
  
  var response = e.data;
  
  switch(response.event) {
    case 'begin':
      handlePaymentBegin(response);
      break;
    case 'success':
      handlePaymentSuccess(response);
      break;
    case 'failure':
      handlePaymentFailure(response);
      break;
    case 'error':
      handlePaymentError(response);
      break;
    case 'abort':
      handlePaymentAbort(response);
      break;
    case 'expired':
      handlePaymentExpired(response);
      break;
  }
});
```

### Callback Function Pattern (Alternative)
```javascript
// Define callback function
function onPaymentComplete(response) {
  if (response.event === 'success') {
    // Process successful payment
    processPayment(response);
  } else if (response.event === 'failure') {
    // Handle payment failure
    handleFailure(response);
  }
}
```

## Authentication and Security

### Signature Generation for iFrame
```javascript
function generateCSGForteSignature(params) {
  // Build signature string
  var signatureString = [
    params.api_access_id,
    params.method,
    params.version_number,
    params.total_amount,
    params.utc_time,
    params.order_number || '',
    params.customer_token || '',
    params.paymethod_token || ''
  ].join('|');
  
  // Generate HMAC-SHA256 signature
  return CryptoJS.HmacSHA256(signatureString, api_secure_key).toString();
}
```

### UTC Time Synchronization
```javascript
// Get UTC time from CSG Forte servers
function getCSGForteUTCTime(callback) {
  $.getJSON('https://sandbox.forte.net/checkout/getUTC?callback=?')
    .done(function(utc) {
      callback(utc);
    });
}
```

## Browser Compatibility

### Supported Browsers
- **Chrome:** Last 3 versions (rolling)
- **Firefox:** Last 3 versions (rolling)  
- **Safari:** Last 3 versions (rolling)
- **Microsoft Edge:** Last 3 versions (rolling)
- **Mobile:** Chrome, Safari, Firefox on mobile devices

### Compatibility Handling
```javascript
// Browser detection for CSG Forte compatibility
function isCSGForteCompatible() {
  var userAgent = navigator.userAgent;
  
  // Block Internet Explorer completely
  if (userAgent.indexOf('MSIE') !== -1 || userAgent.indexOf('Trident') !== -1) {
    return false;
  }
  
  return true;
}
```

## Integration Points in Bento

### Contact Payment Sources Integration
```javascript
// In contact-payment-sources.js
// Replace existing Stripe/iCG button creation with CSG Forte buttons

// Credit Card Button
ui.buttons.makeNode("createCSGForteCC", "button", {
  css: "pda-btnOutline-green",
  text: '<i class="fa fa-plus"></i> CSG Forte Credit Card',
})
.notify("click", {
  type: "paymentMethodRun", 
  data: {
    run: addCCtoCSGForteCustomer.bind(ui, contactId, options),
  },
});

// ACH Button  
ui.buttons.makeNode("createCSGForteACH", "button", {
  css: "pda-btnOutline-blue",
  text: '<i class="fa fa-university"></i> CSG Forte Bank Account',
})
.notify("click", {
  type: "paymentMethodRun",
  data: {
    run: addACHtoCSGForteCustomer.bind(ui, contactId, options),
  },
});
```

### Payment Processing Integration
```javascript
// Success callback processing
function processCSGFortePayment(response) {
  // Extract payment data
  var paymentData = {
    transaction_id: response.trace_number,
    amount: response.total_amount,
    method: response.method_used, // 'visa', 'mast', 'disc', 'amex', 'echeck'
    last_4: response.last_4,
    authorization_code: response.authorization_code,
    response_code: response.response_code
  };
  
  // Send to backend processing
  submitPaymentToBackend(paymentData);
}
```

## Mobile and Responsive Considerations

### Mobile Modal Support
- CSG Forte Checkout v2 fully supports mobile devices
- Modal automatically adapts to screen size
- Touch-friendly interface on tablets and phones

### Responsive Integration
```css
/* Ensure modal displays properly on mobile */
.forte-checkout-modal {
  max-width: 100%;
  margin: 0 auto;
}
```

## Testing Environment Setup

### Sandbox Configuration
```javascript
// Sandbox script for development
<script src="https://sandbox.forte.net/checkout/v2/js"></script>

// Sandbox credentials
api_access_id: "********************************"
location_id: "loc_401809"
```

### Production Configuration  
```javascript
// Production script for live environment
<script src="https://checkout.forte.net/v2/js"></script>

// Production credentials (to be configured)
api_access_id: "[PRODUCTION_API_ACCESS_ID]"
location_id: "[PRODUCTION_LOCATION_ID]"
```

## Success Criteria
- [x] CSG Forte modal integration pattern documented
- [x] Button-triggered modal system understood
- [x] Response handling architecture planned
- [x] Authentication signature process documented
- [x] Browser compatibility requirements identified
- [x] Mobile support capabilities confirmed
- [x] Integration points in Bento identified

## Next Steps
After iFrame integration research completion:
- T21.4: Research error handling patterns
- T21.5: Research fee structures
- T23.x: Begin button implementation
- T24.x: Begin iFrame integration implementation

## Key Advantages of CSG Forte Modal
1. **PCI Compliance:** Payment data never touches merchant server
2. **Security:** HTTPS-only, secure modal overlay
3. **Compatibility:** Works across modern browsers and mobile
4. **Flexibility:** Support for both CC and ACH in same integration
5. **Customization:** Field control and pre-population options

## Notes
- CSG Forte uses modal overlay, not traditional iFrame
- Button-triggered approach similar to existing payment integrations
- Authentication via HMAC-SHA256 signatures required
- UTC time synchronization prevents replay attacks
- Message event listener pattern for response handling
- Mobile-responsive design built-in
