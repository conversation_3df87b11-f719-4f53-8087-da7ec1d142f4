I'll analyze the iCheckGatewayService.php file to understand its architecture and integration within the Bento framework. Let me start by examining the file structure and then gather additional context about how it's used.

Now let me gather more context about how this service is used in the Bento framework by looking for references to it and understanding the broader payment system architecture.

Now let me look for API endpoints and how the service is called from the frontend:

Now let me look for the actual API endpoint that handles service calls:

Let me search for the main API endpoint that handles service calls:

Now let me look at the database interaction patterns and how secret keys are managed:

Now let me look at how the service is actually called and what the duplicate service file looks like:

Perfect! Now I have a comprehensive understanding of the iCheckGatewayService and how it fits into the Bento framework. Let me provide you with a detailed analysis:

## iCheckGatewayService Analysis

### **Architecture Overview**

The iCheckGatewayService is a payment processing service within the Bento framework that handles ACH/eCheck payments through the iCheckGateway provider. Here's how it integrates:

### **1. Framework Integration**

**Service Registration Pattern:**
- Services are auto-loaded by the main API endpoint (`_SRC/pagoda/_get.php`)
- When a request comes in with `&service=iCheckGatewayService`, the framework:
  1. Includes the service file: `require_once APP_ROOT.'services/'. $service .'.php'`
  2. Instantiates the class: `$serviceModule = new $classname($app)`
  3. Calls the requested method: `$serviceModule->$method($_POST)`

**Frontend Integration:**
````javascript path=_SRC/notify/_components/_core/_fields/contact-payment-sources.js mode=EXCERPT
sb.data.db.service(
  "iCheckGatewayService",
  "createICGPaymentObj2",
  paramObj,
  function (response) {
    // Handle response
  }
);
````

### **2. Service Architecture**

**Constructor Pattern:**
- Takes `$sb` (Bento app instance) as dependency injection
- Accesses environment variables for configuration
- Sets up test/production mode detection

**Dependency Access:**
- `$this->sb->pgObjects` - Database operations
- `$this->sb->sendEmail()` - Email notifications
- `$this->sb->updateState()` - Workflow state transitions
- `$this->sb->sendData()` - Response formatting

### **3. Database Interaction Patterns**

**Core pgObjects Methods Used:**
- `getById($objectType, $id, $childObjs)` - Fetch single records
- `where($objectType, $whereClause)` - Query with conditions
- `create($objectType, $data)` - Create new records
- `update($objectType, $data)` - Update existing records
- `getAll($objectType, $childObjs)` - Fetch all records

**Data Relationships:**
- **Proposals** → **Invoices** (via `related_object`)
- **Invoices** → **Payments** (via `payments` array)
- **Projects/Groups** → **Proposals** (via `main_object`)
- **Instances** → Configuration data

### **4. Secret Key Management**

**Environment Variables:**
````php path=_SRC/pagoda/services/iCheckGatewayService.php mode=EXCERPT
$this->stripeSecretKey = getenv('STRIPE_SK');

if (strpos($this->stripeSecretKey, 'sk_test') !== false) {
    $this->testKey = true;
}
````

**Key Points:**
- Uses `getenv()` to access environment variables
- Automatically detects test vs production based on key prefix
- No hardcoded credentials in source code
- Test mode affects email recipients and payment flags

### **5. Surface API**

**Public Methods:**
1. **`createICGPaymentObj($request)`** - Original payment processing method
2. **`createICGPaymentObj2($request)`** - Enhanced version with better invoice handling

**Request Object Structure:**
```php
$request = {
    instanceId: int,
    proposalId: int,
    invoiceIds: array,
    paymentAmount: int (cents),
    paymentFees: int (cents),
    paymentTotalAmount: int (cents),
    percentFee: float,
    flatFee: float,
    icgConfirmationNumber: string,
    icgResponse: object,
    accountLast4: string,
    optionalEmail: string
}
```

### **6. Business Logic Flow**

**Payment Processing:**
1. **Data Gathering** - Fetch instance, proposal, invoices, project data
2. **Invoice Sorting** - Sort by due date (oldest first)
3. **Payment Allocation** - Distribute payment across invoices
4. **Database Updates** - Create payment records, update invoice balances
5. **State Transitions** - Trigger workflow changes on first payment
6. **Notifications** - Send emails to managers and customers

**Multi-Instance Support:**
- Instance-specific email routing
- Different accounting email addresses per instance
- Instance-aware URL generation

### **7. Frontend Integration**

**Payment Flow:**
1. **iFrame Integration** - Customer enters payment info in iCheckGateway iframe
2. **Callback Handling** - JavaScript processes payment response
3. **Service Call** - Frontend calls `createICGPaymentObj2` with payment data
4. **Response Handling** - Updates UI based on success/failure

**Instance Detection:**
````javascript path=_SRC/notify/_components/_core/_fields/contact-payment-sources.js mode=EXCERPT
if (
  appConfig.instance === "infinity" ||
  appConfig.instance === "dreamcatering" ||
  appConfig.instance === "nlp" ||
  appConfig.instance == "rickyvoltz"
) {
  // Show eCheck payment option
}
````

### **8. Error Handling & Logging**

**Debug Features:**
- Pipedream webhook logging for development
- Email-based logging for production debugging
- Comprehensive error catching with `try/catch`
- Output buffering for detailed logging

### **9. File Structure**

**Duplicate Files:**
- `_SRC/pagoda/services/iCheckGatewayService.php` - Main service
- `_SERVICES/app/src/api/services/iCheckGatewayService.php` - Duplicate (legacy?)

**Related Files:**
- `_SRC/pagoda/cron/daily-9-30-updateICGPaymentStatus.php` - Status checking cron
- `_SRC/notify/_components/_core/_fields/contact-payment-sources.js` - Frontend integration

### **10. Key Integration Points**

**Multi-Tenant Architecture:**
- Instance-specific database connections
- Instance-aware email routing
- Environment-specific configurations

**Workflow Integration:**
- Automatic state transitions on first payment
- Project type-based workflow rules
- URL generation for notifications

This service follows Bento's standard patterns for dependency injection, database operations, and multi-tenant architecture. When duplicating it for another payment provider, you'd follow the same structural patterns while adapting the payment-specific logic.
