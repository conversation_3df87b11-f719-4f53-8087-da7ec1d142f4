<?php

class CSGForteService
{
    private $forteApiAccessId = null;
    private $forteSecureKey = null;
    private $forteLocationId = null;
    private $forteEnvironment = null;
    private $currentEnv = null;
    private $testMode = false;

    function __construct($sb)
    {
        $this->sb = $sb;
        
        // Determine environment and load appropriate credentials
        $this->currentEnv = getenv('CURRENT_ENV');
        
        if ($this->currentEnv === 'production') {
            // Load production credentials
            $this->forteApiAccessId = getenv('CSG_FORTE_API_ACCESS_ID_PROD');
            $this->forteSecureKey = getenv('CSG_FORTE_SECURE_KEY_PROD');
            $this->forteLocationId = getenv('CSG_FORTE_LOCATION_ID_PROD');
            $this->forteEnvironment = getenv('CSG_FORTE_ENVIRONMENT_PROD');
            $this->testMode = false;
        } else {
            // Load development/sandbox credentials
            $this->forteApiAccessId = getenv('CSG_FORTE_API_ACCESS_ID');
            $this->forteSecureKey = getenv('CSG_FORTE_SECURE_KEY');
            $this->forteLocationId = getenv('CSG_FORTE_LOCATION_ID');
            $this->forteEnvironment = getenv('CSG_FORTE_ENVIRONMENT');
            $this->testMode = true;
        }
    }

    // PRIVATE METHODS
    
    /**
     * Generate HMAC-SHA256 signature for CSG Forte API requests
     */
    private function generateSignature($data)
    {
        // Sort parameters alphabetically by key
        ksort($data);
        
        // Create query string
        $queryString = http_build_query($data);
        
        // Generate HMAC-SHA256 signature
        $signature = hash_hmac('sha256', $queryString, $this->forteSecureKey);
        
        return $signature;
    }

    /**
     * Get current UTC time from Forte servers (prevents replay attacks)
     */
    private function getForteUTCTime()
    {
        // For now, use local UTC time
        // In production, you might want to fetch from Forte's time endpoint
        return gmdate('Y-m-d\TH:i:s\Z');
    }

    /**
     * Make HTTP request to CSG Forte API
     */
    private function makeForteAPIRequest($endpoint, $data = [], $method = 'GET')
    {
        $baseUrl = $this->testMode ? 
            'https://sandbox.forte.net/api/v3' : 
            'https://api.forte.net/api/v3';
        
        $url = $baseUrl . $endpoint;
        
        $curl = curl_init();
        curl_setopt_array($curl, [
            CURLOPT_URL => $url,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_TIMEOUT => 30,
            CURLOPT_HTTPHEADER => [
                'Content-Type: application/json',
                'X-Forte-Auth-Account-Id: ' . $this->forteApiAccessId
            ]
        ]);
        
        if ($method === 'POST') {
            curl_setopt($curl, CURLOPT_POST, true);
            curl_setopt($curl, CURLOPT_POSTFIELDS, json_encode($data));
        }
        
        $response = curl_exec($curl);
        $httpCode = curl_getinfo($curl, CURLINFO_HTTP_CODE);
        curl_close($curl);
        
        return [
            'status_code' => $httpCode,
            'response' => json_decode($response, true),
            'raw_response' => $response
        ];
    }

    // PUBLIC METHODS

    /**
     * Test API connectivity and credentials
     */
    public function testConnection($request)
    {
        try {
            // Test endpoint - get account information
            $result = $this->makeForteAPIRequest('/accounts/' . $this->forteApiAccessId);
            
            $returnObj = new stdClass();
            $returnObj->success = ($result['status_code'] === 200);
            $returnObj->environment = $this->forteEnvironment;
            $returnObj->test_mode = $this->testMode;
            $returnObj->api_access_id = $this->forteApiAccessId;
            $returnObj->location_id = $this->forteLocationId;
            $returnObj->status_code = $result['status_code'];
            
            if ($returnObj->success) {
                $returnObj->message = 'CSG Forte API connection successful';
                $returnObj->account_data = $result['response'];
            } else {
                $returnObj->message = 'CSG Forte API connection failed';
                $returnObj->error = $result['response'];
            }
            
            return $this->sb->sendData($returnObj, 1);
            
        } catch (Exception $e) {
            $returnObj = new stdClass();
            $returnObj->success = false;
            $returnObj->message = 'CSG Forte API test failed';
            $returnObj->error = $e->getMessage();
            
            return $this->sb->sendData($returnObj, 0);
        }
    }

    /**
     * Create secure payment button configuration
     */
    public function createPaymentButton($request)
    {
        try {
            $utcTime = $this->getForteUTCTime();
            $orderNumber = 'ORDER_' . time() . '_' . rand(1000, 9999);
            
            // Prepare signature data
            $signatureData = [
                'api_access_id' => $this->forteApiAccessId,
                'location_id' => $this->forteLocationId,
                'method' => 'sale',
                'version_number' => '2.0',
                'total_amount' => $request->totalAmount,
                'utc_time' => $utcTime,
                'order_number' => $orderNumber
            ];
            
            // Generate secure signature on server
            $signature = $this->generateSignature($signatureData);
            
            // Return button configuration (safe to send to frontend)
            $buttonData = [
                'api_access_id' => $this->forteApiAccessId,
                'location_id' => $this->forteLocationId,
                'method' => 'sale',
                'version_number' => '2.0',
                'total_amount' => $request->totalAmount,
                'utc_time' => $utcTime,
                'order_number' => $orderNumber,
                'hash_method' => 'sha256',
                'signature' => $signature,
                'callback' => 'onForteCallback',
                'environment' => $this->forteEnvironment,
                'script_url' => $this->testMode 
                    ? "https://sandbox.forte.net/checkout/v2/js"
                    : "https://checkout.forte.net/v2/js"
            ];
            
            $returnObj = new stdClass();
            $returnObj->success = true;
            $returnObj->buttonData = $buttonData;
            $returnObj->test_mode = $this->testMode;
            
            return $this->sb->sendData($returnObj, 1);
            
        } catch (Exception $e) {
            return $this->sb->sendData($e, 0);
        }
    }

    /**
     * Verify callback signature from CSG Forte
     */
    public function verifyCallbackSignature($request)
    {
        try {
            $callbackData = $request->callbackData;
            $receivedSignature = $callbackData['signature'];
            
            // Remove signature from data for verification
            unset($callbackData['signature']);
            
            // Generate expected signature
            $expectedSignature = $this->generateSignature($callbackData);
            
            $returnObj = new stdClass();
            $returnObj->success = hash_equals($expectedSignature, $receivedSignature);
            $returnObj->expected_signature = $expectedSignature;
            $returnObj->received_signature = $receivedSignature;
            
            return $this->sb->sendData($returnObj, 1);
            
        } catch (Exception $e) {
            return $this->sb->sendData($e, 0);
        }
    }

    /**
     * Process successful payment callback
     */
    public function processPaymentCallback($request)
    {
        try {
            // Verify signature first
            $signatureVerification = $this->verifyCallbackSignature($request);
            
            if (!$signatureVerification->success) {
                throw new Exception('Invalid callback signature');
            }
            
            // TODO: Implement payment processing logic similar to iCheckGatewayService
            // - Create payment records in database
            // - Update invoice balances
            // - Send notification emails
            // - Handle state transitions
            
            $returnObj = new stdClass();
            $returnObj->success = true;
            $returnObj->message = 'Payment callback processed successfully';
            $returnObj->test_mode = $this->testMode;
            
            return $this->sb->sendData($returnObj, 1);
            
        } catch (Exception $e) {
            return $this->sb->sendData($e, 0);
        }
    }

    /**
     * Get service configuration for frontend
     */
    public function getServiceConfig($request)
    {
        $returnObj = new stdClass();
        $returnObj->success = true;
        $returnObj->environment = $this->forteEnvironment;
        $returnObj->test_mode = $this->testMode;
        $returnObj->script_url = $this->testMode 
            ? "https://sandbox.forte.net/checkout/v2/js"
            : "https://checkout.forte.net/v2/js";
        
        return $this->sb->sendData($returnObj, 1);
    }
}
