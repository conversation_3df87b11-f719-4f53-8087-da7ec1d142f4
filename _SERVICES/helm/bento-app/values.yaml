imageName:
imageTag:

configmap: bentoproduction
config:
  BEN<PERSON>_DATABASE_NAME:
  BENTO_DATABASE_PASSWORD:
  BENTO_DATABASE_PORT:
  BENTO_DATABASE_SSL_FLAG:
  BENTO_DATABASE_URL:
  BENTO_DATABASE_USER:
  BENTO_DOCS_DATABASE_NAME:
  BENTO_DOCUMENTS_URL:
  CURRENT_ENV:
  DATA_ENDPOINT:
  ICG_APIKEY:
  ICG_APIKEY_DREAM:
  ICG_GATEWAYLIVEMODE:
  ICG_SITEID:
  ICG_SITEID_DREAM:
  ICG_SITEKEY:
  ICG_SITEKEY_DREAM:
  MERGE_ENDPOINT:
  STRIPE_PK:
  STRIPE_SK:
  CSG_FORTE_API_ACCESS_ID:
  CSG_FORTE_SECURE_KEY:
  CSG_FORTE_LOCATION_ID:
  CSG_FORTE_ENVIRONMENT:
  CSG_FORTE_API_ACCESS_ID_PROD:
  CSG_FORTE_SECURE_KEY_PROD:
  CSG_FORTE_LOCATION_ID_PROD:
  CSG_FORTE_ENVIRONMENT_PROD:

service:
  portName: http
  port: 80
  type: ClusterIP

ingress:
  issuer: letsencrypt-prod-bento
  issuerSecret: tls-secret-bento
  issuerEmail: <EMAIL>
  foundationGroup: foundationgroup.bento-dev.republix.com
